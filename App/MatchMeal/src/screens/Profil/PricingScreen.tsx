import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

const PricingScreen = ({ navigation }: any) => {
  // Données simulées
  const pricing = {
    free: {
      title: "Gratuit",
      price: "0€",
      period: "",
      features: [
        "10 likes par jour",
        "3 superlikes par jour",
        "Accès aux recettes de base",
        "Statistiques limitées",
      ],
    },
    premium: {
      title: "Premium",
      price: "9,99€",
      period: "/mois",
      features: [
        "Likes illimités",
        "10 superlikes par jour",
        "Accès à toutes les recettes",
        "Statistiques avancées",
        "Recettes exclusives",
        "Support prioritaire",
      ],
    },
    superlikes: {
      pack5: { count: 5, price: "1,99€" },
      pack10: { count: 10, price: "3,49€" },
      pack25: { count: 25, price: "7,99€" },
    },
  };

  const handleSubscribe = () => {
    Alert.alert(
      "Abonnement Premium",
      "Fonctionnalité d'abonnement à implémenter",
      [{ text: "OK" }]
    );
  };

  const handleBuySuperlikes = (count: number, price: string) => {
    Alert.alert(
      "Achat de Superlikes",
      `Acheter ${count} superlikes pour ${price}`,
      [
        { text: "Annuler", style: "cancel" },
        { text: "Acheter", onPress: () => console.log(`Achat de ${count} superlikes`) },
      ]
    );
  };

  const renderFeature = (feature: string, index: number) => (
    <View key={index} style={styles.featureRow}>
      <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
      <Text style={styles.featureText}>{feature}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={{ padding: 20 }}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Abonnements</Text>
          <View style={{ width: 24 }} />
        </View>

        {/* Plans d'abonnement */}
        <View style={styles.plansContainer}>
          {/* Plan Gratuit */}
          <View style={styles.planCard}>
            <View style={styles.planHeader}>
              <Text style={styles.planTitle}>{pricing.free.title}</Text>
              <View style={styles.priceContainer}>
                <Text style={styles.price}>{pricing.free.price}</Text>
                <Text style={styles.period}>{pricing.free.period}</Text>
              </View>
            </View>
            <View style={styles.featuresContainer}>
              {pricing.free.features.map((feature, index) => 
                renderFeature(feature, index)
              )}
            </View>
          </View>

          {/* Plan Premium */}
          <View style={[styles.planCard, styles.premiumCard]}>
            <View style={styles.premiumBadge}>
              <Text style={styles.premiumBadgeText}>POPULAIRE</Text>
            </View>
            <View style={styles.planHeader}>
              <Text style={[styles.planTitle, styles.premiumTitle]}>
                {pricing.premium.title}
              </Text>
              <View style={styles.priceContainer}>
                <Text style={[styles.price, styles.premiumPrice]}>
                  {pricing.premium.price}
                </Text>
                <Text style={styles.period}>{pricing.premium.period}</Text>
              </View>
            </View>
            <View style={styles.featuresContainer}>
              {pricing.premium.features.map((feature, index) => 
                renderFeature(feature, index)
              )}
            </View>
            <TouchableOpacity style={styles.subscribeButton} onPress={handleSubscribe}>
              <Text style={styles.subscribeButtonText}>S'abonner</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Section Superlikes */}
        <View style={styles.superlikesSection}>
          <Text style={styles.sectionTitle}>Acheter des Superlikes</Text>
          <Text style={styles.sectionSubtitle}>
            Boostez vos chances de match avec des superlikes supplémentaires
          </Text>
          
          <View style={styles.superlikesContainer}>
            {Object.entries(pricing.superlikes).map(([key, pack]) => (
              <TouchableOpacity
                key={key}
                style={styles.superlikeCard}
                onPress={() => handleBuySuperlikes(pack.count, pack.price)}
              >
                <Ionicons name="star" size={24} color="#FFD700" />
                <Text style={styles.superlikeCount}>{pack.count}</Text>
                <Text style={styles.superlikePrice}>{pack.price}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { 
    flex: 1, 
    backgroundColor: "#fff" 
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 30,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#333",
  },
  
  // Plans
  plansContainer: {
    marginBottom: 30,
    flexDirection: "row",
    flexWrap: "wrap",
  },
  planCard: {
    backgroundColor: "#f9f9f9",
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    position: "relative",
    width: "48%",
  },
  premiumCard: {
    backgroundColor: "#FFF8E1",
    borderWidth: 2,
    borderColor: "#FFD700",
  },
  premiumBadge: {
    position: "absolute",
    top: -8,
    right: 20,
    backgroundColor: "#FFD700",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  premiumBadgeText: {
    fontSize: 12,
    fontWeight: "700",
    color: "#333",
  },
  planHeader: {
    marginBottom: 20,
  },
  planTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: "#333",
    marginBottom: 8,
  },
  premiumTitle: {
    color: "#E65100",
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  price: {
    fontSize: 32,
    fontWeight: "700",
    color: "#333",
  },
  premiumPrice: {
    color: "#E65100",
  },
  period: {
    fontSize: 16,
    color: "#666",
    marginLeft: 4,
  },
  featuresContainer: {
    marginBottom: 20,
  },
  featureRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  featureText: {
    fontSize: 16,
    color: "#333",
    marginLeft: 8,
  },
  subscribeButton: {
    backgroundColor: "#FFD700",
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  subscribeButtonText: {
    fontSize: 18,
    fontWeight: "700",
    color: "#333",
  },
  
  // Superlikes
  superlikesSection: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#333",
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: "#666",
    marginBottom: 20,
  },
  superlikesContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  superlikeCard: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "#f9f9f9",
    paddingVertical: 20,
    marginHorizontal: 4,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  superlikeCount: {
    fontSize: 18,
    fontWeight: "700",
    color: "#333",
    marginTop: 8,
  },
  superlikePrice: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFD700",
    marginTop: 4,
  },
});

export default PricingScreen;

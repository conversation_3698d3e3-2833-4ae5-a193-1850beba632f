import React from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

const ProfileScreen = ({ navigation }: any) => {
  // Données simulées pour l'utilisateur
  const user = {
    name: "<PERSON>",
    age: 27,
    bio: "Amoureux de cuisine italienne 🍕 | Fan de découvertes culinaires ✨",
    avatar: "https://i.pravatar.cc/400",
    isPremium: false, // Statut premium
    stats: {
      recipesMatched: 24, // Recettes matchées
      recipesViewed: 156, // Recettes vues
      countriesFound: 12, // Pays trouvés
    },
    dailyLimits: {
      likesRemaining: 8, // Likes restants aujourd'hui
      superlikesRemaining: 2, // Superlikes restants aujourd'hui
      maxLikes: 10, // Limite quotidienne de likes
      maxSuperlikes: 3, // Limite quotidienne de superlikes
    },
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={{ padding: 20 }}>
        {/* Header */}
        <Text style={styles.header}>Mon profil</Text>

        {/* Avatar + Infos */}
        <View style={styles.profileSection}>
          <Image source={{ uri: user.avatar }} style={styles.avatar} />
          <Text style={styles.name}>
            {user.name}, {user.age}
          </Text>
          <Text style={styles.bio}>{user.bio}</Text>
        </View>

        {/* Bannière Premium */}
        <TouchableOpacity
          style={[styles.premiumBanner, user.isPremium ? styles.premiumActive : styles.premiumInactive]}
          onPress={() => navigation.navigate("Pricing")}
        >
          <View style={styles.premiumContent}>
            <Ionicons
              name={user.isPremium ? "diamond" : "diamond-outline"}
              size={24}
              color={user.isPremium ? "#FFD700" : "#666"}
            />
            <Text style={[styles.premiumText, user.isPremium ? styles.premiumTextActive : styles.premiumTextInactive]}>
              {user.isPremium ? "Membre Premium ✨" : "Passer au Premium"}
            </Text>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </View>
        </TouchableOpacity>

        {/* Statistiques principales */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Ionicons name="heart" size={22} color="#FF6B6B" />
            <Text style={styles.statNumber}>{user.stats.recipesMatched}</Text>
            <Text style={styles.statLabel}>Matchés</Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="eye" size={22} color="#4A90E2" />
            <Text style={styles.statNumber}>{user.stats.recipesViewed}</Text>
            <Text style={styles.statLabel}>Vues</Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="globe-outline" size={22} color="#3B82F6" />
            <Text style={styles.statNumber}>{user.stats.countriesFound}</Text>
            <Text style={styles.statLabel}>Pays trouvés</Text>
          </View>
        </View>

        {/* Likes quotidiens */}
        <View style={styles.dailyLimitsContainer}>
          <Text style={styles.sectionTitle}>Aujourd'hui</Text>
          <View style={styles.limitsRow}>
            <View style={styles.limitCard}>
              <Ionicons name="heart" size={20} color="#FF6B6B" />
              <Text style={styles.limitNumber}>{user.dailyLimits.likesRemaining}</Text>
              <Text style={styles.limitLabel}>Likes restants</Text>
              <Text style={styles.limitTotal}>sur {user.dailyLimits.maxLikes}</Text>
            </View>

            <View style={styles.limitCard}>
              <Ionicons name="star" size={20} color="#FFD700" />
              <Text style={styles.limitNumber}>{user.dailyLimits.superlikesRemaining}</Text>
              <Text style={styles.limitLabel}>Superlikes</Text>
              <Text style={styles.limitTotal}>sur {user.dailyLimits.maxSuperlikes}</Text>
            </View>
          </View>
        </View>

        {/* Options */}
        <View style={styles.options}>

          <TouchableOpacity
            style={styles.optionButton}
            onPress={() => navigation.navigate("Settings")}
          >
            <Ionicons name="settings-outline" size={22} color="#444" />
            <Text style={styles.optionText}>Paramètres</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  header: { fontSize: 24, fontWeight: "700", color: "#222", marginBottom: 20 },
  profileSection: { alignItems: "center", marginBottom: 20 },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 2,
    borderColor: "#FF6B6B",
  },
  name: { fontSize: 20, fontWeight: "700", marginTop: 12, color: "#333" },
  bio: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    marginTop: 6,
    paddingHorizontal: 40,
  },

  // Bannière Premium
  premiumBanner: {
    marginBottom: 20,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  premiumActive: {
    backgroundColor: "#FFF8E1",
    borderWidth: 1,
    borderColor: "#FFD700",
  },
  premiumInactive: {
    backgroundColor: "#f9f9f9",
    borderWidth: 1,
    borderColor: "#eee",
  },
  premiumContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  premiumText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    fontWeight: "600",
  },
  premiumTextActive: {
    color: "#E65100",
  },
  premiumTextInactive: {
    color: "#333",
  },

  // Statistiques
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "#f9f9f9",
    paddingVertical: 16,
    marginHorizontal: 6,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  statNumber: { fontSize: 18, fontWeight: "700", color: "#222", marginTop: 6 },
  statLabel: { fontSize: 13, color: "#666", marginTop: 2 },

  // Section titre
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 12,
  },

  // Limites quotidiennes
  dailyLimitsContainer: {
    marginBottom: 20,
  },
  limitsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  limitCard: {
    flex: 1,
    alignItems: "center",
    backgroundColor: "#f9f9f9",
    paddingVertical: 16,
    marginHorizontal: 6,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  limitNumber: {
    fontSize: 20,
    fontWeight: "700",
    color: "#222",
    marginTop: 6
  },
  limitLabel: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
    textAlign: "center",
  },
  limitTotal: {
    fontSize: 11,
    color: "#999",
    marginTop: 1
  },

  // Options
  options: { marginTop: 10 },
  optionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  optionText: { fontSize: 16, marginLeft: 12, color: "#333", fontWeight: "500" },
});

export default ProfileScreen;
